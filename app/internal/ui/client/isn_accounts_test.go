package client

import (
	"encoding/json"
	"net/http"
	"net/http/httptest"
	"testing"
)

func TestLookupServiceAccountByClientID(t *testing.T) {
	tests := []struct {
		name           string
		clientID       string
		mockResponse   []ServiceAccountDetails
		mockStatusCode int
		expectError    bool
		expectedResult *ServiceAccountLookupResponse
	}{
		{
			name:     "successful lookup",
			clientID: "sa_example-org_abc123",
			mockResponse: []ServiceAccountDetails{
				{
					AccountID: "account-123",
					ClientID:  "sa_example-org_abc123",
				},
				{
					AccountID: "account-456",
					ClientID:  "sa_other-org_def456",
				},
			},
			mockStatusCode: http.StatusOK,
			expectError:    false,
			expectedResult: &ServiceAccountLookupResponse{
				AccountID: "account-123",
				ClientID:  "sa_example-org_abc123",
			},
		},
		{
			name:           "client ID not found",
			clientID:       "sa_nonexistent_xyz789",
			mockResponse:   []ServiceAccountDetails{},
			mockStatusCode: http.StatusOK,
			expectError:    true,
			expectedResult: nil,
		},
		{
			name:           "API error",
			clientID:       "sa_example-org_abc123",
			mockResponse:   nil,
			mockStatusCode: http.StatusInternalServerError,
			expectError:    true,
			expectedResult: nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock server
			server := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				// Verify the request
				if r.URL.Path != "/api/admin/service-accounts" {
					t.Errorf("Expected path /api/admin/service-accounts, got %s", r.URL.Path)
				}
				if r.Header.Get("Authorization") != "Bearer test-token" {
					t.Errorf("Expected Authorization header 'Bearer test-token', got %s", r.Header.Get("Authorization"))
				}

				w.WriteHeader(tt.mockStatusCode)
				if tt.mockStatusCode == http.StatusOK && tt.mockResponse != nil {
					json.NewEncoder(w).Encode(tt.mockResponse)
				}
			}))
			defer server.Close()

			// Create client
			client := NewClient(server.URL)

			// Call the method
			result, err := client.LookupServiceAccountByClientID("test-token", tt.clientID)

			// Check error expectation
			if tt.expectError && err == nil {
				t.Error("Expected error but got none")
			}
			if !tt.expectError && err != nil {
				t.Errorf("Unexpected error: %v", err)
			}

			// Check result
			if tt.expectedResult != nil {
				if result == nil {
					t.Error("Expected result but got nil")
				} else {
					if result.AccountID != tt.expectedResult.AccountID {
						t.Errorf("Expected AccountID %s, got %s", tt.expectedResult.AccountID, result.AccountID)
					}
					if result.ClientID != tt.expectedResult.ClientID {
						t.Errorf("Expected ClientID %s, got %s", tt.expectedResult.ClientID, result.ClientID)
					}
				}
			} else if result != nil {
				t.Errorf("Expected nil result but got %+v", result)
			}
		})
	}
}

// ServiceAccountDetails matches the structure from admin.go
type ServiceAccountDetails struct {
	AccountID string `json:"account_id"`
	ClientID  string `json:"client_id"`
}
